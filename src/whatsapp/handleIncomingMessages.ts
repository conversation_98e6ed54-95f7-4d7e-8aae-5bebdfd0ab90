import makeWASocket, { type MessageUpsertType, type WAMessage, isJidBroadcast, isJidStatusBroadcast } from "baileys";
import { mastra } from "../mastra/index.js";
import { RuntimeContext } from "@mastra/core/di";
import type { ChatbotRuntimeContext } from "../mastra/agents/chatbot-agent.js";
import { formatMessageDateTime } from "../utils/messageParser.js";
import { getMessageText, getSenderName, shouldBotRespond } from "./helpers.js";

export async function handleIncomingMessages(
    { messages, type }: { messages: WAMessage[]; type: MessageUpsertType },
    sock: ReturnType<typeof makeWASocket>
) {
    if (type !== "notify") return;

    for (const msg of messages) {
        if (!msg.message) continue;
        if (msg.key.fromMe) continue; // Ignore messages sent by the bot
        if (isJidBroadcast(msg.key.remoteJid!) || isJidStatusBroadcast(msg.key.remoteJid!)) continue;

        const messageText = getMessageText(msg);
        if (!messageText) continue;

        // Check if the bot should respond to this message (mentioned or replied to)
        if (!shouldBotRespond(msg, sock)) {
            // If bot shouldn't respond, manually add the message to conversation history
            await addMessageToMemory(messageText, msg, sock);
            continue;
        }

        const userJid = msg.key.participant || msg.key.remoteJid!;

        // Extract user ID and chat ID for proper memory management
        const userFormattedId = userJid.replace("@s.whatsapp.net", "").replace("@g.us", ""); // User who sent the message
        const chatJid = msg.key.remoteJid!; // Chat where message was sent

        console.log("Received message:", messageText, "from:", userFormattedId);

        // Extract sender information
        const senderName = await getSenderName(msg, sock);

        // Show typing indicator
        await sock.sendPresenceUpdate("composing", msg.key.remoteJid!);

        // Here you can integrate with your AI agent
        const response = await generateBotResponse(messageText, userJid, chatJid, msg, senderName);

        if (response) {
            // Stop typing indicator and send response
            await sock.sendPresenceUpdate("paused", msg.key.remoteJid!);
            await sock.sendMessage(msg.key.remoteJid!, { text: response });
        } else {
            // Stop typing indicator if no response
            await sock.sendPresenceUpdate("paused", msg.key.remoteJid!);
        }
    }
}

async function generateBotResponse(
    messageText: string,
    userJid: string,
    chatJid: string,
    message: WAMessage,
    senderName: string
): Promise<string | null> {
    try {
        const agent = mastra.getAgent("chatbotAgent");
        const runtimeContext = new RuntimeContext<ChatbotRuntimeContext>();
        runtimeContext.set("config_model", "gemini-2.5-flash");
        runtimeContext.set("config_botName", "Jeff");

        // Include current user information with the question and timestamp
        const timestamp = message.messageTimestamp ? Number(message.messageTimestamp) : Date.now() / 1000;
        const currentMessageDateTime = formatMessageDateTime(timestamp);
        const formattedMessage = `${senderName} [${currentMessageDateTime}]:\n${messageText}`;
        console.log("formattedMessage:", formattedMessage);

        const response = await agent.generate(formattedMessage, {
            resourceId: userJid, // Use user's WhatsApp ID for memory persistence
            threadId: chatJid, // Use chat ID as thread ID for conversation context
            runtimeContext,
        });

        console.log(response);

        return response.text || null;
    } catch (error) {
        console.error("Error generating bot response:", error);
        return null;
    }
}

async function addMessageToMemory(
    messageText: string,
    message: WAMessage,
    sock: ReturnType<typeof makeWASocket>
): Promise<void> {
    try {
        const userJid = message.key.participant ?? message.key.remoteJid;
        const chatJid = message.key.remoteJid;

        if (!userJid || !chatJid) {
            console.log("Missing user or chat JID, skipping message storage");
            return;
        }

        // Extract sender information
        const senderName = await getSenderName(message, sock);

        // Format the message with timestamp
        const timestamp = message.messageTimestamp ? Number(message.messageTimestamp) : Date.now() / 1000;
        const currentMessageDateTime = formatMessageDateTime(timestamp);
        const formattedMessage = `${senderName} [${currentMessageDateTime}]:\n${messageText}`;

        // Use the agent to add the message to memory without generating a response
        const agent = mastra.getAgent("chatbotAgent");
        const runtimeContext = new RuntimeContext<ChatbotRuntimeContext>();
        runtimeContext.set("config_model", "gemini-2.5-flash");
        runtimeContext.set("config_botName", "Jeff");

        // Create a special system message that instructs the agent to not respond
        // but still process the message for memory storage
        const systemMessage =
            "SYSTEM: This is a message from a group chat where you were not mentioned. Add this message to memory but do not generate any response. Simply acknowledge with 'MEMORY_STORED' and nothing else.";

        const response = await agent.generate(`${systemMessage}\n\nUser message: ${formattedMessage}`, {
            resourceId: userJid, // Use user's WhatsApp ID for memory persistence
            threadId: chatJid, // Use chat ID as thread ID for conversation context
            runtimeContext,
        });

        console.log(`Added message to memory for ${senderName} in thread ${chatJid} - Response: ${response.text}`);
    } catch (error) {
        console.error("Error adding message to memory:", error);
    }
}
