import { MCPClient } from "@mastra/mcp";

export const webSearchMCP = new MCPClient({
    servers: {
        web_search: {
            command: "npx",
            args: ["-y", "exa-mcp-server", "--tools=web_search_exa"],
            env: {
                EXA_API_KEY: "4e01cf2d-7b82-4ee3-ad97-21b7fd805e78",
            },
        },
    },
});

const toolsCollection = await webSearchMCP.getTools();

// console.log({ web_search_web_search_exa: toolsCollection?.web_search_web_search_exa });

export const webSearchTool = toolsCollection;

// import { createTool } from "@mastra/core/tools";
// import Exa from "exa-js";
// import { z } from "zod";

// const exa = new Exa("4e01cf2d-7b82-4ee3-ad97-21b7fd805e78");

// const getSearchResults = async (question: string) => {
//     console.log("Using tool to fetch search results for", question);
//     return await exa.answer(question, {
//         text: true,
//         outputSchema: {
//             type: "object",
//             required: ["answer"],
//             additionalProperties: false,
//             properties: {
//                 answer: {
//                     type: "string",
//                     description: "The solution to all your problems...",
//                 },
//             },
//         },
//     });
// };

// export const webSearchTool = createTool({
//     id: "Get Search Results",
//     description: `Fetches the current search results for a given question`,
//     inputSchema: z.object({
//         question: z.string().describe("Question to search for"),
//     }),
//     outputSchema: z.object({
//         answer: z.string(),
//     }),
//     execute: async ({ context: { question } }) => {
//         const result = await getSearchResults(question);
//         return { answer: result.answer as string };
//     },
// });
